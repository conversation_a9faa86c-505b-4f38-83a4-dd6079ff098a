"""
側鏈資源耗盡攻擊模擬器 (Sidelink Resource Exhaustion Attack Simulator)

此攻擊方法試圖擾亂整個通信資源池的使用效率，迫使合法車輛在不利的通信條件下運行。
攻擊者通過佔用相對「乾淨」(干擾較小) 的通信資源，迫使其他車輛選擇較為「嘈雜」(干擾較大) 的資源，
增加訊息碰撞和接收失敗的概率。

攻擊步驟：
1. 監聽階段：觀察資源使用情況，確定哪些是相對空閒(乾淨)的資源
2. 攻擊階段：在識別出的相對乾淨的資源上發送一階SCI訊息，佔用這些資源
"""

import random
from src.CONFIG import *
from src.simulators.simulator import NRV2XSimulator
from src.ue import VehicleUE
from src.mobility import MODEL_LINEAR
from src.logger import Logger
from src.attacker_model.sidelink_resource_exhaustion import SidelinkResourceExhaustionUE

class Simulator3(NRV2XSimulator):
    def __init__(self, simulation_index, normal_ue_count=5, attacker_ue_count=1):
        super().__init__(simulation_index, num_normal_ues=normal_ue_count, num_attacker_ues=0)  # 設置為0，避免基類創建攻擊者
        self.normal_ue_count = normal_ue_count
        self.attacker_ue_count = attacker_ue_count
        
    def setup_scenario(self):
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_START", UE_ID="System")
        print(f"Time {self.env.now}: Setting up Resource Exhaustion Attack scenario...")

        # 創建正常車輛
        for i in range(self.normal_ue_count):
            ue_id = f"NormalUE-{i}"
            initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
            initial_dir_deg = 90
            initial_speed_mps = random.uniform(8, 15)
            
            # 創建UE特定日誌
            ue_logger_instance = Logger(filename=f"{ue_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
            self.ue_loggers.append(ue_logger_instance)
            
            ue = VehicleUE(self.env, ue_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                            initial_pos=initial_pos, initial_speed=initial_speed_mps,
                            prsvp=PRSVP, ue_specific_logger=ue_logger_instance)
            self.normal_ues.append(ue)
            self.all_ues.append(ue)
            self.ues_objects_dict[ue_id] = ue
            self.mobility_model.initialize_ue_state(ue_id, model_type=MODEL_LINEAR, 
                                                    initial_pos=initial_pos, 
                                                    initial_speed_mps=initial_speed_mps, 
                                                    initial_direction_deg=initial_dir_deg)
            self.global_logger.log_event(Time_ms=self.env.now, EventType="UE_CREATED", UE_ID=ue_id, Details="Type: Normal")
            print(f"  {ue_id} created.")

        # 創建資源耗盡攻擊者
        for i in range(self.attacker_ue_count):
            attacker_id = f"ResourceExhaustionAttacker-{i}"
            initial_pos = (random.uniform(0, AREA_WIDTH), random.uniform(0, AREA_HEIGHT))
            initial_dir_deg = 90
            initial_speed_mps = random.uniform(8, 15)
            
            # 創建攻擊者特定日誌
            attacker_logger_instance = Logger(filename=f"{attacker_id}.csv", log_dir=self.ue_log_dir, fieldnames=UE_SPECIFIC_LOG_FIELDNAMES)
            self.ue_loggers.append(attacker_logger_instance)
            
            attacker = SidelinkResourceExhaustionUE(
                self.env, attacker_id, self.resource_pool, self.comm_model, self.global_logger, self.mobility_model,
                initial_pos=initial_pos, initial_speed=initial_speed_mps,
                jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM,
                disruption_power_dbm=DISRUPTION_POWER_DBM,
                ue_specific_logger=attacker_logger_instance
            )
            
            self.attacker_ues.append(attacker)
            self.all_ues.append(attacker)
            self.ues_objects_dict[attacker_id] = attacker
            self.mobility_model.initialize_ue_state(attacker_id, model_type=MODEL_LINEAR, 
                                                    initial_pos=initial_pos, 
                                                    initial_speed_mps=initial_speed_mps, 
                                                    initial_direction_deg=initial_dir_deg)
            self.global_logger.log_event(Time_ms=self.env.now, EventType="ATTACKER_CREATED", UE_ID=attacker_id, Details="Type: ResourceExhaustion")
            print(f"  {attacker_id} created.")

        # 設置UE之間的相互引用（用於距離計算等）
        for ue in self.all_ues:
            ue.all_other_ues = {other_ue.ue_id: other_ue for other_ue in self.all_ues if other_ue.ue_id != ue.ue_id}

        self.global_logger.log_event(Time_ms=self.env.now, EventType="SCENARIO_SETUP_COMPLETE", UE_ID="System", 
                              Details=f"Created {NUM_NORMAL_UES} normal UEs and {NUM_ATTACKER_UES} resource exhaustion attackers")
        print(f"Scenario setup complete. Total UEs: {len(self.all_ues)}")

    def run_simulation(self):
        """運行模擬並收集結果"""
        self.global_logger.log_event(Time_ms=self.env.now, EventType="SIM_RUN_START", UE_ID="System")
        print(f"Starting Resource Exhaustion Attack simulation for {SIM_DURATION_MS}ms...")

        # 設置場景
        self.setup_scenario()
        self.env.process(self.update_mobility_periodically())

        # 啟動所有UE的接收器進程
        for ue in self.all_ues:
            self.env.process(ue.receiver_process())

        self.global_logger.log_event(Time_ms=self.env.now, EventType="SIMULATION_START", UE_ID="System")

        # 運行模擬
        self.env.run(until=SIM_DURATION_MS)

        self.global_logger.log_event(Time_ms=self.env.now, EventType="SIMULATION_END", UE_ID="System", Details=f"Simulation finished at {self.env.now:.2f} ms")
        print(f"Resource Exhaustion Attack simulation completed at time {self.env.now}ms")

        # 關閉所有日誌記錄器
        self.global_logger.close()
        for ue_logger in self.ue_loggers:
            ue_logger.close()

        print("All loggers closed. Simulation data saved.")

if __name__ == "__main__":
    # 運行單個模擬實例
    sim = Simulator3(simulation_index="simulation_3")
    sim.run_simulation()
    print("Resource Exhaustion Attack simulation completed successfully!")
