# 側鏈資源耗盡攻擊者修改總結

## 修改概述

根據您的要求，我已經成功修改了 `sidelink_resource_exhaustion` 攻擊者，使其符合以下攻擊描述：

> 攻擊者透過監聽並解碼旁鏈控制資訊 (SCI)，讀取其中的資源預留 (RR) 位元，從而預測其他車輛在未來會繼續使用哪些資源。然後，它會策略性地佔用那些預計會是空閒的資源，目的是「填滿」整個資源池的空隙。

**攻擊邏輯**: "我知道車輛 A 下一輪將會使用資源 X，所以我去佔用資源 Y 和 Z。"

## 主要修改內容

### 1. 攻擊邏輯重新設計

**原始邏輯** (半雙工資源池擾亂):
- 監聽 → 識別乾淨資源 → 隨機攻擊乾淨資源

**新邏輯** (基於 SCI 解碼的智能攻擊):
- 監聽並解碼 SCI → 預測車輛未來資源使用 → 策略性佔用預計空閒的資源

### 2. 新增數據結構

```python
# SCI 解碼相關數據結構
self.decoded_sci_messages = {}  # {sender_id: latest_sci_content}
self.vehicle_resource_patterns = {}  # {sender_id: {prsvp, last_resources, prediction_time}}
self.predicted_occupied_resources = set()  # 預測會被佔用的資源
```

### 3. SCI 解碼功能

在 `receiver_process()` 中新增了 SCI 解碼邏輯：
- 識別 SCI 類型的傳輸
- 計算 SINR 並判斷解碼成功率
- 提取 `pssch_resources` (資源預留位元)
- 儲存解碼成功的 SCI 消息

### 4. 資源預測算法

新增 `_predict_future_resources()` 方法：
- 基於 SCI 中的 `prsvp` (資源預留週期) 參數
- 預測車輛未來 3 個週期內的資源使用
- 考慮 `pssch_resources` 中的所有資源

### 5. 策略性攻擊目標選擇

新增 `_select_strategic_attack_targets()` 方法：
- **策略 1**: 優先攻擊有預測佔用資源的時槽中的空隙
- **策略 2**: 對剩餘可攻擊資源進行隨機攻擊
- 實現「填滿空隙」的攻擊邏輯

### 6. 智能分析階段

修改 `listen_and_analyze_phase()`:
- **SCI 解碼分析**: 處理解碼的 SCI 消息
- **資源預測**: 基於 PRSVP 預測未來資源使用
- **策略選擇**: 排除預測佔用的資源，選擇攻擊目標

## 技術實現細節

### SCI 消息處理
```python
if signal_type == "SCI" and content:
    # 計算 SINR 用於解碼判斷
    sinr_db = self.comm_model.calculate_sinr_db(received_power_dbm, interference_power_linear, self.comm_model.noise_power_linear)
    
    # 嘗試解碼 SCI
    is_decoded = self.comm_model.is_decoded_successfully(sinr_db, "SCI")
    
    if is_decoded:
        # 成功解碼 SCI，儲存解碼結果
        self.decoded_sci_messages[sender_id] = content
```

### 資源預測邏輯
```python
def _predict_future_resources(self, sender_id, pssch_resources, prsvp, timestamp):
    predicted_resources = set()
    prediction_cycles = 3  # 預測未來3個週期
    
    for resource in pssch_resources:
        slot, subchannel = resource
        # 基於 PRSVP 預測未來的資源使用時間點
        for cycle in range(1, prediction_cycles + 1):
            future_slot = slot + (cycle * prsvp)
            predicted_resources.add((future_slot, subchannel))
    
    return predicted_resources
```

### 策略性攻擊選擇
```python
# 排除當前已使用的資源和預測會被佔用的資源
predicted_occupied_relative = set((slot % 100, subchannel) for slot, subchannel in self.predicted_occupied_resources)
available_for_attack = all_possible_resources - used_resources_relative - predicted_occupied_relative

# 選擇攻擊目標：策略性填滿空隙
self.attack_candidates = self._select_strategic_attack_targets(available_for_attack, predicted_occupied_relative)
```

## 攻擊效果

這個修改後的攻擊者能夠：

1. **智能監聽**: 解碼 SCI 消息，獲取車輛的資源預留資訊
2. **預測分析**: 基於 `pssch_resources` 和 `prsvp` 預測車輛未來資源使用
3. **策略攻擊**: 
   - 避免攻擊預測會被佔用的資源
   - 優先攻擊有車輛活動的時槽中的空隙
   - 實現「填滿空隙」的攻擊效果
4. **資源耗盡**: 迫使正常車輛在資源重選時只能選擇干擾較高的資源

## 測試結果

✅ 所有功能測試通過：
- SCI 消息解碼能力
- 基於 pssch_resources 的資源預測
- 策略性攻擊目標選擇
- '填滿空隙' 攻擊邏輯實現

## 使用方法

修改後的攻擊者使用方式與原版相同，但攻擊邏輯已完全改變為基於 SCI 解碼的智能攻擊模式。攻擊者會自動：
1. 監聽並解碼 SCI 消息
2. 預測車輛未來資源使用
3. 策略性選擇攻擊目標
4. 執行「填滿空隙」攻擊

這個實現完全符合您描述的攻擊邏輯："我知道車輛 A 下一輪將會使用資源 X，所以我去佔用資源 Y 和 Z。"
