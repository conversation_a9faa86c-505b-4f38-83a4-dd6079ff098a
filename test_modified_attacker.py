#!/usr/bin/env python3
"""
測試修改後的側鏈資源耗盡攻擊者
驗證基於 SCI 解碼的智能攻擊邏輯是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.attacker_model.sidelink_resource_exhaustion import SidelinkResourceExhaustionUE
from src.CONFIG import *
import simpy

def test_attacker_initialization():
    """測試攻擊者初始化"""
    print("=== 測試攻擊者初始化 ===")
    
    # 創建模擬環境
    env = simpy.Environment()
    
    # 模擬必要的依賴對象
    class MockLogger:
        def log_event(self, **kwargs):
            print(f"LOG: {kwargs}")
    
    class MockMobilityModel:
        def get_ue_state(self, ue_id):
            return {"pos": (0, 0)}
    
    class MockResourcePool:
        def __init__(self):
            self.active_transmissions = {}
        def log_jamming(self, ue_id, resource, power, duration):
            print(f"JAMMING: UE {ue_id} jamming {resource} with {power}dBm for {duration}ms")
    
    class MockCommModel:
        def __init__(self):
            self.noise_power_linear = 1e-12
        def calculate_distance(self, pos1, pos2):
            return 100.0
        def calculate_received_power_dbm(self, tx_power, distance):
            return tx_power - 50
        def calculate_sinr_db(self, signal_power, interference, noise):
            return 20.0
        def is_decoded_successfully(self, sinr, signal_type):
            return sinr > 10.0
    
    # 創建攻擊者實例
    try:
        attacker = SidelinkResourceExhaustionUE(
            env=env,
            ue_id="TestAttacker",
            resource_pool=MockResourcePool(),
            comm_model=MockCommModel(),
            global_logger=MockLogger(),
            mobility_model=MockMobilityModel(),
            initial_pos=(0, 0),
            initial_speed=50,
            ue_specific_logger=MockLogger(),
            jamming_power_dbm=30,
            disruption_power_dbm=25
        )
        
        print("✓ 攻擊者初始化成功")
        print(f"  - UE ID: {attacker.ue_id}")
        print(f"  - 干擾功率: {attacker.jamming_power_dbm}dBm")
        print(f"  - SCI 解碼數據結構已初始化: {hasattr(attacker, 'decoded_sci_messages')}")
        print(f"  - 車輛資源模式追蹤已初始化: {hasattr(attacker, 'vehicle_resource_patterns')}")
        print(f"  - 預測資源集合已初始化: {hasattr(attacker, 'predicted_occupied_resources')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 攻擊者初始化失敗: {e}")
        return False

def test_sci_prediction_methods():
    """測試 SCI 預測相關方法"""
    print("\n=== 測試 SCI 預測方法 ===")
    
    env = simpy.Environment()
    
    class MockLogger:
        def log_event(self, **kwargs):
            pass
    
    class MockMobilityModel:
        def get_ue_state(self, ue_id):
            return {"pos": (0, 0)}
    
    class MockResourcePool:
        def __init__(self):
            self.active_transmissions = {}
        def log_jamming(self, ue_id, resource, power, duration):
            pass
    
    class MockCommModel:
        def __init__(self):
            self.noise_power_linear = 1e-12
        def calculate_distance(self, pos1, pos2):
            return 100.0
        def calculate_received_power_dbm(self, tx_power, distance):
            return tx_power - 50
        def calculate_sinr_db(self, signal_power, interference, noise):
            return 20.0
        def is_decoded_successfully(self, sinr, signal_type):
            return sinr > 10.0
    
    try:
        attacker = SidelinkResourceExhaustionUE(
            env=env,
            ue_id="TestAttacker",
            resource_pool=MockResourcePool(),
            comm_model=MockCommModel(),
            global_logger=MockLogger(),
            mobility_model=MockMobilityModel(),
            initial_pos=(0, 0),
            initial_speed=50,
            ue_specific_logger=None,
            jamming_power_dbm=30,
            disruption_power_dbm=25
        )
        
        # 測試預測方法
        test_resources = [(10, 2), (25, 3)]
        predicted = attacker._predict_future_resources("TestUE", test_resources, 100, 0)
        
        print(f"✓ 預測方法測試成功")
        print(f"  - 輸入資源: {test_resources}")
        print(f"  - 預測結果數量: {len(predicted)}")
        print(f"  - 預測結果樣本: {list(predicted)[:5]}")
        
        # 測試攻擊目標選擇方法
        available_resources = {(i, j) for i in range(10) for j in range(3)}
        predicted_occupied = {(5, 1), (7, 2)}
        attack_targets = attacker._select_strategic_attack_targets(available_resources, predicted_occupied)
        
        print(f"✓ 攻擊目標選擇測試成功")
        print(f"  - 可用資源數量: {len(available_resources)}")
        print(f"  - 預測佔用資源: {predicted_occupied}")
        print(f"  - 選擇的攻擊目標數量: {len(attack_targets)}")
        print(f"  - 攻擊目標樣本: {attack_targets[:5]}")
        
        return True
        
    except Exception as e:
        print(f"✗ SCI 預測方法測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("開始測試修改後的側鏈資源耗盡攻擊者...")
    print("=" * 50)
    
    success_count = 0
    total_tests = 2
    
    # 執行測試
    if test_attacker_initialization():
        success_count += 1
    
    if test_sci_prediction_methods():
        success_count += 1
    
    # 總結
    print("\n" + "=" * 50)
    print(f"測試完成: {success_count}/{total_tests} 通過")
    
    if success_count == total_tests:
        print("✓ 所有測試通過！修改後的攻擊者可以正常工作。")
        print("\n攻擊者新功能:")
        print("1. ✓ SCI 消息解碼能力")
        print("2. ✓ 基於 pssch_resources 的資源預測")
        print("3. ✓ 策略性攻擊目標選擇")
        print("4. ✓ '填滿空隙' 攻擊邏輯實現")
        return True
    else:
        print("✗ 部分測試失敗，需要進一步調試。")
        return False

if __name__ == "__main__":
    main()
