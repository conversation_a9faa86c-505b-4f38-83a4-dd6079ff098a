"""
側鏈資源耗盡攻擊 (Sidelink Resource Exhaustion Attack) - 基於 SCI 解碼的智能攻擊

此攻擊模型透過監聽並解碼旁鏈控制資訊 (SCI)，讀取其中的資源預留 (RR) 位元，
從而預測其他車輛在未來會繼續使用哪些資源。然後，它會策略性地佔用那些預計會是空閒的資源，
目的是「填滿」整個資源池的空隙。

攻擊邏輯：
1.  **監聽與解碼週期 (100ms)**: 攻擊者在此期間進入純監聽模式，不發送任何信號。
    - 利用接收器持續監測資源池中的所有活動
    - 特別關注 SCI 消息的解碼，提取其中的 pssch_resources 資訊
    - 記錄每個車輛的資源使用模式和預留資訊

2.  **智能分析與預測**: 監聽週期結束後，攻擊者分析收集到的 SCI 數據。
    - 解析每個車輛的 pssch_resources，了解其資源預留模式
    - 基於 PRSVP (資源預留週期) 預測車輛未來的資源使用
    - 識別出預計會空閒的資源空隙
    - 攻擊邏輯："我知道車輛 A 下一輪將會使用資源 X，所以我去佔用資源 Y 和 Z。"

3.  **策略性攻擊週期 (100ms)**: 根據預測結果執行精準攻擊。
    - 暫停監聽功能，進入攻擊模式
    - 在預測的空閒資源上發送干擾信號，填滿資源池空隙
    - 迫使正常車輛在資源重選時只能選擇干擾較高的資源

4.  **週期性重複**: 不斷重複「監聽解碼 -> 預測分析 -> 策略攻擊」的循環。
"""

import random
import simpy
from src.CONFIG import *

class SidelinkResourceExhaustionUE:
    def __init__(self, env, ue_id, resource_pool, comm_model, global_logger, mobility_model,
                 initial_pos=(0,0), initial_speed=0,
                 jamming_power_dbm=ATTACKER_JAMMING_POWER_DBM,
                 disruption_power_dbm=DISRUPTION_POWER_DBM,
                 ue_specific_logger=None):
        # 基本UE屬性初始化
        self.env = env
        self.ue_id = ue_id
        self.resource_pool = resource_pool
        self.comm_model = comm_model
        self.global_logger = global_logger
        self.ue_logger = ue_specific_logger
        self.mobility_model = mobility_model
        
        self.pos = initial_pos
        self.speed = initial_speed
        
        self.is_attacker = True
        self.all_other_ues = {}
        
        # 攻擊者特定參數
        self.jamming_power_dbm = jamming_power_dbm
        self.disruption_power_dbm = disruption_power_dbm

        # 儲存監聽到的資源使用情況 {resource: total_rsrp}
        self.sensed_resource_rsrp = {}

        # SCI 解碼相關數據結構
        self.decoded_sci_messages = {}  # {sender_id: latest_sci_content}
        self.vehicle_resource_patterns = {}  # {sender_id: {prsvp, last_resources, prediction_time}}
        self.predicted_occupied_resources = set()  # 預測會被佔用的資源

        # 儲存下一攻擊週期要攻擊的資源候選列表 (relative_slot, subchannel)
        self.attack_candidates = []
        # 半雙工狀態旗標
        self.is_listening = False

        self.global_logger.log_event(Time_ms=self.env.now, EventType="RESOURCE_DISRUPTOR_INIT", UE_ID=self.ue_id,
                              Details=f"側鏈資源耗盡攻擊者初始化完成 - 干擾功率: {self.jamming_power_dbm}dBm, 擾亂功率: {self.disruption_power_dbm}dBm, 採用SCI解碼智能攻擊邏輯, 位置: {self.pos}")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="ATTACKER_INIT",
                                   Details=f"智能資源耗盡攻擊者啟動 - 基於SCI解碼的預測攻擊，初始位置: {self.pos}, 速度: {self.speed}")

        # 啟動攻擊者的運行邏輯和接收器進程
        self.action = env.process(self.attacker_run_logic())
        self.receiver_process_action = env.process(self.receiver_process())

    def attacker_run_logic(self):
        """攻擊者的主要運行邏輯，實現基於 SCI 解碼的智能攻擊方案。"""
        self.global_logger.log_event(Time_ms=self.env.now, EventType="ATTACKER_LOGIC_START", UE_ID=self.ue_id,
                                   Details="開始執行智能攻擊循環：SCI解碼監聽 -> 預測分析 -> 策略性攻擊")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="INTELLIGENT_ATTACK_START",
                                   Details="啟動基於SCI解碼的智能攻擊模式，目標：通過預測車輛資源使用來策略性填滿資源池空隙")

        cycle_count = 0
        while True:
            cycle_count += 1
            cycle_start_time = self.env.now

            self.global_logger.log_event(Time_ms=cycle_start_time, EventType="INTELLIGENT_ATTACK_CYCLE_BEGIN", UE_ID=self.ue_id,
                                       Details=f"開始第 {cycle_count} 輪智能攻擊循環 - SCI解碼預測攻擊")

            # 1. 監聽與分析階段
            yield self.env.process(self.listen_and_analyze_phase())
            # 2. 攻擊階段
            yield self.env.process(self.attack_phase())

            cycle_end_time = self.env.now
            cycle_duration = cycle_end_time - cycle_start_time

            self.global_logger.log_event(Time_ms=cycle_end_time, EventType="INTELLIGENT_ATTACK_CYCLE_COMPLETE", UE_ID=self.ue_id,
                                       Details=f"第 {cycle_count} 輪智能攻擊循環完成，耗時: {cycle_duration}ms")

            if self.ue_logger:
                self.ue_logger.log_event(Time_ms=cycle_end_time, UE_ID=self.ue_id, EventType="CYCLE_STATS",
                                       Details=f"循環 {cycle_count} 統計 - 持續時間: {cycle_duration}ms, 攻擊候選數: {len(self.attack_candidates)}")

    def listen_and_analyze_phase(self):
        """監聽與解碼分析階段 (100ms): 監聽並解碼 SCI 消息，預測車輛資源使用模式。"""
        listen_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=listen_phase_start_time, EventType="ATTACKER_LISTEN_PHASE_START", UE_ID=self.ue_id,
                                   Details="開始監聽解碼階段，持續100ms，目標：解碼SCI消息並預測資源使用")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=listen_phase_start_time, UE_ID=self.ue_id, EventType="ATTACKER_LISTEN_START",
                                   Details=f"進入SCI解碼監聽模式，當前位置: {self.pos}")

        # 清空先前的感知數據並開始監聽
        previous_observations = len(self.sensed_resource_rsrp)
        previous_sci_count = len(self.decoded_sci_messages)
        self.sensed_resource_rsrp.clear()
        self.decoded_sci_messages.clear()  # 清空上一輪的 SCI 數據
        self.is_listening = True

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=self.env.now, UE_ID=self.ue_id, EventType="SENSING_RESET",
                                   Details=f"清除前次數據 - 觀測: {previous_observations} 筆, SCI: {previous_sci_count} 筆")

        # 監聽100ms
        yield self.env.timeout(100)

        # 停止監聽並開始分析
        self.is_listening = False
        listen_phase_end_time = self.env.now

        # --- SCI 解碼分析子階段 ---
        # 分析解碼的 SCI 消息，預測車輛未來資源使用
        self.predicted_occupied_resources.clear()
        sci_analysis_results = {}

        for sender_id, sci_content in self.decoded_sci_messages.items():
            # 提取資源預留資訊
            prsvp = sci_content.get("prsvp", 100)  # 資源預留週期
            pssch_resources = sci_content.get("pssch_resources", [])
            timestamp = sci_content.get("timestamp", self.env.now)

            # 更新車輛資源模式
            self.vehicle_resource_patterns[sender_id] = {
                "prsvp": prsvp,
                "last_resources": pssch_resources,
                "prediction_time": timestamp,
                "sci_content": sci_content
            }

            # 基於 PRSVP 預測未來資源使用
            predicted_resources = self._predict_future_resources(sender_id, pssch_resources, prsvp, timestamp)
            self.predicted_occupied_resources.update(predicted_resources)

            sci_analysis_results[sender_id] = {
                "resources": pssch_resources,
                "prsvp": prsvp,
                "predicted_count": len(predicted_resources)
            }

        # --- 傳統資源感知分析 ---
        used_resources_relative = set()
        signal_strength_stats = {'min': float('inf'), 'max': float('-inf'), 'avg': 0}
        total_rsrp = 0

        for (absolute_slot, subchannel), rsrp in self.sensed_resource_rsrp.items():
            relative_slot = absolute_slot % 100
            used_resources_relative.add((relative_slot, subchannel))
            total_rsrp += rsrp
            signal_strength_stats['min'] = min(signal_strength_stats['min'], rsrp)
            signal_strength_stats['max'] = max(signal_strength_stats['max'], rsrp)

        if len(self.sensed_resource_rsrp) > 0:
            signal_strength_stats['avg'] = total_rsrp / len(self.sensed_resource_rsrp)
        else:
            signal_strength_stats = {'min': 0, 'max': 0, 'avg': 0}

        # --- 策略性攻擊目標選擇 ---
        # 找出所有可能的資源
        all_possible_resources = set((slot, subchannel) for slot in range(100) for subchannel in range(NUM_SUBCHANNELS))

        # 排除當前已使用的資源和預測會被佔用的資源
        predicted_occupied_relative = set((slot % 100, subchannel) for slot, subchannel in self.predicted_occupied_resources)
        available_for_attack = all_possible_resources - used_resources_relative - predicted_occupied_relative

        # 選擇攻擊目標：策略性填滿空隙
        self.attack_candidates = self._select_strategic_attack_targets(available_for_attack, predicted_occupied_relative)

        # 計算資源利用率和攻擊統計
        total_resources = 100 * NUM_SUBCHANNELS
        resource_utilization = len(used_resources_relative) / total_resources * 100
        predicted_utilization = len(predicted_occupied_relative) / total_resources * 100
        available_for_attack_ratio = len(available_for_attack) / total_resources * 100

        self.global_logger.log_event(Time_ms=listen_phase_end_time, EventType="ATTACKER_ANALYSIS_COMPLETE",
                                      UE_ID=self.ue_id, Details=f"SCI分析完成 - SCI解碼: {len(self.decoded_sci_messages)}, 攻擊候選: {len(self.attack_candidates)}, 當前使用率: {resource_utilization:.1f}%, 預測使用率: {predicted_utilization:.1f}%, 可攻擊比例: {available_for_attack_ratio:.1f}%")
        if self.ue_logger:
            sci_summary = f"SCI解碼: {len(self.decoded_sci_messages)}筆"
            prediction_summary = f"預測佔用: {len(predicted_occupied_relative)}個資源"
            attack_summary = f"攻擊目標: {len(self.attack_candidates)}個"
            self.ue_logger.log_event(Time_ms=listen_phase_end_time, UE_ID=self.ue_id, EventType="ATTACKER_CANDIDATES_SELECTED",
                                     Details=f"智能攻擊候選選擇 - {sci_summary}, {prediction_summary}, {attack_summary}, 樣本: {str(self.attack_candidates[:3])}")

    def attack_phase(self):
        """策略性攻擊階段 (100ms): 基於 SCI 預測結果，策略性佔用預計空閒的資源。"""
        attack_phase_start_time = self.env.now
        self.global_logger.log_event(Time_ms=attack_phase_start_time, EventType="ATTACKER_ATTACK_PHASE_START",
                                      UE_ID=self.ue_id, Details=f"開始策略性攻擊階段，持續100ms，目標資源數: {len(self.attack_candidates)}, 功率: {self.jamming_power_dbm}dBm，攻擊邏輯: 填滿資源池空隙")
        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=attack_phase_start_time, UE_ID=self.ue_id, EventType="ATTACKER_ATTACK_START",
                                     Details=f"進入智能攻擊模式 - 基於SCI預測的策略性攻擊，目標數: {len(self.attack_candidates)}, 當前位置: {self.pos}")

        scheduled_attacks = 0
        attack_timeline = []

        if self.attack_candidates:
            for relative_slot, subchannel in self.attack_candidates:
                # 計算攻擊的絕對時間點
                attack_time = attack_phase_start_time + relative_slot
                attack_timeline.append((attack_time, relative_slot, subchannel))
                # 使用一個獨立的 process 來處理未來的攻擊事件，避免阻塞主循環
                self.env.process(self.jam_resource(attack_time, subchannel))
                scheduled_attacks += 1

        # 計算攻擊覆蓋率
        if len(self.attack_candidates) > 0:
            time_slots_covered = len(set(slot for slot, _ in self.attack_candidates))
            coverage_percentage = time_slots_covered / 100 * 100  # 100個時槽中覆蓋的百分比
        else:
            coverage_percentage = 0

        self.global_logger.log_event(Time_ms=attack_phase_start_time, EventType="ATTACK_DEPLOYMENT_COMPLETE",
                                      UE_ID=self.ue_id, Details=f"攻擊部署完成 - 已排程攻擊: {scheduled_attacks}, 時槽覆蓋率: {coverage_percentage:.1f}%")

        # 等待攻擊階段完成
        yield self.env.timeout(100)

        attack_phase_end_time = self.env.now
        actual_duration = attack_phase_end_time - attack_phase_start_time
        self.global_logger.log_event(Time_ms=attack_phase_end_time, EventType="ATTACKER_ATTACK_PHASE_END", UE_ID=self.ue_id,
                                   Details=f"攻擊階段結束，實際持續時間: {actual_duration}ms，執行攻擊數: {scheduled_attacks}")

    def jam_resource(self, attack_time, subchannel):
        """在指定的時間點對指定的子通道執行一次干擾。"""
        # 等待直到指定的攻擊時間
        time_to_wait = attack_time - self.env.now
        if time_to_wait > 0:
            yield self.env.timeout(time_to_wait)

        # 執行干擾
        current_time = self.env.now
        # 在這個時間點，slot 就是 current_time
        resource_to_attack = (current_time, subchannel)

        self.resource_pool.log_jamming(self.ue_id, resource_to_attack, self.jamming_power_dbm, 1) # 干擾持續1ms

        # 計算攻擊延遲
        expected_attack_time = attack_time
        actual_delay = current_time - expected_attack_time if time_to_wait > 0 else 0

        self.global_logger.log_event(Time_ms=current_time, EventType="JAM_ATTACK_EXECUTED", UE_ID=self.ue_id,
                                   Details=f"干擾攻擊執行 - 資源: {resource_to_attack}, 功率: {self.jamming_power_dbm}dBm, 持續: 1ms, 延遲: {actual_delay}ms")

        if self.ue_logger:
            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id,
                                    EventType="ATTACKER_JAM_EXECUTE",
                                    Resource_Details=str(resource_to_attack),
                                    Details=f"干擾執行 - 功率: {self.jamming_power_dbm}dBm, 持續: 1ms, 預期時間: {expected_attack_time}, 實際延遲: {actual_delay}ms")

    def receiver_process(self):
        """持續監聽其他UE的傳輸。只有在 is_listening 為 True 時才記錄數據。"""
        while True:
            # 以較高的頻率輪詢以捕捉所有傳輸
            yield self.env.timeout(RECEIVER_POLLING_INTERVAL_MS)
            
            if not self.is_listening:
                continue

            current_time = self.env.now
            # 更新自身位置
            current_mobility_state = self.mobility_model.get_ue_state(self.ue_id)
            self.pos = current_mobility_state["pos"]
            
            active_transmissions_copy = dict(self.resource_pool.active_transmissions)
            
            for trans_info in active_transmissions_copy.values():
                # 檢查傳輸是否在當前輪詢時間點附近活躍
                if not (trans_info["start_time"] <= current_time < trans_info["end_time"]):
                    continue

                sender_id = trans_info["ue_id"]
                if sender_id == self.ue_id:
                    continue

                resource = trans_info["resource"]
                tx_power_dbm = trans_info["tx_power"]

                sender_ue_object = self.all_other_ues.get(sender_id)
                if not sender_ue_object:
                    continue

                distance = self.comm_model.calculate_distance(self.pos, sender_ue_object.pos)
                if distance > MAX_COMM_RANGE_M:
                    continue

                received_power_dbm = self.comm_model.calculate_received_power_dbm(tx_power_dbm, distance)
                if received_power_dbm < MIN_RX_POWER_DBM:
                    continue

                # 記錄感知到的資源使用情況
                if resource not in self.sensed_resource_rsrp:
                    self.sensed_resource_rsrp[resource] = received_power_dbm

                # 嘗試解碼 SCI 消息
                signal_type = trans_info.get("type", "")
                content = trans_info.get("content", {})

                if signal_type == "SCI" and content:
                    # 計算 SINR 用於解碼判斷
                    interference_power_linear = 0.0  # 簡化處理，假設無干擾
                    sinr_db = self.comm_model.calculate_sinr_db(received_power_dbm, interference_power_linear, self.comm_model.noise_power_linear)

                    # 嘗試解碼 SCI
                    is_decoded = self.comm_model.is_decoded_successfully(sinr_db, "SCI")

                    if is_decoded:
                        # 成功解碼 SCI，儲存解碼結果
                        self.decoded_sci_messages[sender_id] = content

                        # 記錄 SCI 解碼成功
                        if self.ue_logger:
                            pssch_resources = content.get("pssch_resources", [])
                            prsvp = content.get("prsvp", 100)
                            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id,
                                                   EventType="SCI_DECODED_SUCCESS",
                                                   Related_UE_ID=sender_id,
                                                   Details=f"SCI解碼成功 - 資源: {pssch_resources}, PRSVP: {prsvp}, SINR: {sinr_db:.1f}dB")
                    else:
                        # SCI 解碼失敗
                        if self.ue_logger and len(self.sensed_resource_rsrp) % 20 == 1:
                            self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id,
                                                   EventType="SCI_DECODE_FAILED",
                                                   Related_UE_ID=sender_id,
                                                   Details=f"SCI解碼失敗 - SINR: {sinr_db:.1f}dB, 距離: {distance:.1f}m")

                # 詳細的監聽日誌（僅在調試模式下）
                if self.ue_logger and len(self.sensed_resource_rsrp) % 10 == 1:  # 每10個觀測記錄一次
                    self.ue_logger.log_event(Time_ms=current_time, UE_ID=self.ue_id,
                                           EventType="RESOURCE_SENSED",
                                           Details=f"感知到資源使用 - 資源: {resource}, 發送者: {sender_id}, 距離: {distance:.1f}m, 接收功率: {received_power_dbm:.1f}dBm, 累計觀測: {len(self.sensed_resource_rsrp)}")

    def _predict_future_resources(self, sender_id, pssch_resources, prsvp, timestamp):
        """基於 SCI 中的資源預留資訊預測車輛未來的資源使用"""
        predicted_resources = set()
        current_time = self.env.now

        # 預測未來幾個 PRSVP 週期內的資源使用
        prediction_cycles = 3  # 預測未來3個週期

        for resource in pssch_resources:
            if not resource:
                continue

            slot, subchannel = resource

            # 基於 PRSVP 預測未來的資源使用時間點
            for cycle in range(1, prediction_cycles + 1):
                future_slot = slot + (cycle * prsvp)
                predicted_resources.add((future_slot, subchannel))

        return predicted_resources

    def _select_strategic_attack_targets(self, available_for_attack, predicted_occupied_relative):
        """策略性選擇攻擊目標，實現'填滿空隙'的攻擊邏輯"""
        import random

        attack_targets = []

        if not available_for_attack:
            return attack_targets

        # 按時槽分組可攻擊的資源
        available_by_slot = {}
        for slot, subchannel in available_for_attack:
            if slot not in available_by_slot:
                available_by_slot[slot] = []
            available_by_slot[slot].append(subchannel)

        # 策略1: 優先攻擊有預測佔用資源的時槽中的空隙
        priority_slots = set()
        for slot, subchannel in predicted_occupied_relative:
            if slot in available_by_slot:
                priority_slots.add(slot)

        # 對優先時槽進行攻擊
        for slot in priority_slots:
            if slot in available_by_slot:
                # 在該時槽中選擇一個或多個子通道進行攻擊
                available_subchannels = available_by_slot[slot]
                num_to_attack = min(len(available_subchannels), max(1, len(available_subchannels) // 2))
                chosen_subchannels = random.sample(available_subchannels, num_to_attack)

                for subchannel in chosen_subchannels:
                    attack_targets.append((slot, subchannel))

        # 策略2: 對剩餘的可攻擊資源進行隨機攻擊
        remaining_slots = set(available_by_slot.keys()) - priority_slots
        for slot in remaining_slots:
            if len(attack_targets) >= 50:  # 限制攻擊目標數量
                break
            available_subchannels = available_by_slot[slot]
            chosen_subchannel = random.choice(available_subchannels)
            attack_targets.append((slot, chosen_subchannel))

        return attack_targets